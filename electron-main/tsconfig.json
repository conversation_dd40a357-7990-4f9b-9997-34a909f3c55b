{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "sourceMap": true, "noImplicitAny": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/trading/*": ["src/trading/*"], "@/services/*": ["src/services/*"], "@/ipc/*": ["src/ipc/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/market/*": ["src/market/*"]}, "types": ["node", "electron"]}, "include": ["src/**/*", "testing/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}