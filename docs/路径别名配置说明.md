# 路径别名配置说明

## 概述

为了提高代码的可维护性和可读性，项目已配置了路径别名，避免使用复杂的相对路径导入（如 `../../types`）。

## 配置的别名

### 前端项目 (src/)

| 别名 | 实际路径 | 用途 |
|------|----------|------|
| `@/*` | `./src/*` | 根目录通用别名 |
| `@/components/*` | `./src/components/*` | React 组件 |
| `@/hooks/*` | `./src/hooks/*` | 自定义 Hooks |
| `@/services/*` | `./src/services/*` | 业务服务 |
| `@/types/*` | `./src/types/*` | TypeScript 类型定义 |
| `@/utils/*` | `./src/utils/*` | 工具函数 |
| `@/data/*` | `./src/data/*` | 静态数据和模板 |
| `@/pages/*` | `./src/pages/*` | 页面组件 |
| `@/communication/*` | `./src/communication/*` | 通信模块 |

### Electron 主进程 (electron-main/src/)

| 别名 | 实际路径 | 用途 |
|------|----------|------|
| `@/*` | `src/*` | 根目录通用别名 |
| `@/trading/*` | `src/trading/*` | 交易系统核心 |
| `@/services/*` | `src/services/*` | 系统服务 |
| `@/ipc/*` | `src/ipc/*` | IPC 通信处理 |
| `@/utils/*` | `src/utils/*` | 工具函数 |
| `@/types/*` | `src/types/*` | 类型定义 |
| `@/market/*` | `src/market/*` | 市场数据相关 |

## 配置文件

### 1. TypeScript 配置 (tsconfig.json)

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/services/*": ["./src/services/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/data/*": ["./src/data/*"],
      "@/pages/*": ["./src/pages/*"],
      "@/communication/*": ["./src/communication/*"]
    }
  }
}
```

### 2. Vite 配置 (vite.config.ts)

```typescript
export default defineConfig({
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@/components": path.resolve(__dirname, "./src/components"),
      "@/hooks": path.resolve(__dirname, "./src/hooks"),
      "@/services": path.resolve(__dirname, "./src/services"),
      "@/types": path.resolve(__dirname, "./src/types"),
      "@/utils": path.resolve(__dirname, "./src/utils"),
      "@/data": path.resolve(__dirname, "./src/data"),
      "@/pages": path.resolve(__dirname, "./src/pages"),
      "@/communication": path.resolve(__dirname, "./src/communication")
    }
  }
});
```

### 3. Electron 主进程配置 (electron-main/tsconfig.json)

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/trading/*": ["src/trading/*"],
      "@/services/*": ["src/services/*"],
      "@/ipc/*": ["src/ipc/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"],
      "@/market/*": ["src/market/*"]
    }
  }
}
```

## 使用示例

### 之前（相对路径）
```typescript
import { Task } from '../../types';
import { brokerManager } from '../../services/brokerManager';
import { useElectronDashboard } from '../../hooks/useElectronDashboard';
```

### 之后（别名路径）
```typescript
import { Task } from '@/types';
import { brokerManager } from '@/services/brokerManager';
import { useElectronDashboard } from '@/hooks/useElectronDashboard';
```

## 优势

1. **可读性更好**: 路径更清晰，一眼就能看出导入的模块类型
2. **重构友好**: 移动文件时不需要更新大量的相对路径
3. **减少错误**: 避免相对路径层级错误
4. **IDE 支持**: 更好的自动补全和跳转支持
5. **团队协作**: 统一的导入风格，提高代码一致性

## 注意事项

1. 别名配置需要在 TypeScript 和构建工具中同时配置
2. IDE 可能需要重启才能识别新的别名配置
3. 确保所有团队成员都了解别名规则
4. 在添加新的目录时，考虑是否需要添加对应的别名

## 已更新的文件

以下文件已从相对路径导入更新为别名导入：

- `src/components/Dashboard/TaskConfigModal.tsx`
- `src/pages/Dashboard/Dashboard.tsx`
- `src/components/Dashboard/BrokerManagerModal.tsx`
- `src/data/strategyTemplates.ts`
- `src/pages/TradingDemo.tsx`
- `src/hooks/useElectronDashboard.ts`
- `src/components/Dashboard/TaskDetailsModal.tsx`
- `src/components/Dashboard/TaskCard.tsx`
- `src/components/Dashboard/TaskList.tsx`
- `src/hooks/useTradingDemo.ts`
- `src/communication/electronTradingClient.ts`
- `src/components/Dashboard/OptimizedTaskCard.tsx`
- `src/components/Dashboard/ErrorBoundary.tsx`
- `src/components/Dashboard/SmartUpdates.tsx`
- `src/components/Dashboard/TaskLogModal.tsx`
- `src/pages/CounterTest.tsx`
- `src/types/index.ts`
- `electron-main/src/services/TradingSystemService.ts`
- `electron-main/src/ipc/handlers.ts`
- `electron-main/src/trading/TradingSystem.ts`
