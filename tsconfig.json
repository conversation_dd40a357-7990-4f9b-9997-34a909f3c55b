{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "module": "ESNext",
        "skipLibCheck": true,
        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",
        /* Path mapping */
        "baseUrl": ".",
        "paths": {
            "@/*": [
                "./src/*"
            ],
            "@/components/*": [
                "./src/components/*"
            ],
            "@/hooks/*": [
                "./src/hooks/*"
            ],
            "@/services/*": [
                "./src/services/*"
            ],
            "@/types/*": [
                "./src/types/*"
            ],
            "@/utils/*": [
                "./src/utils/*"
            ],
            "@/data/*": [
                "./src/data/*"
            ],
            "@/pages/*": [
                "./src/pages/*"
            ],
            "@/communication/*": [
                "./src/communication/*"
            ]
        },
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true
    },
    "include": [
        "src"
    ],
    "references": [
        {
            "path": "./tsconfig.node.json"
        }
    ]
}